"use client";

import React, { useEffect, useState } from "react";
import { CheckoutProvider, useCheckout } from "./checkout-context";
import {
  CheckoutSteps,
  CustomerInformation,
  DeliveryAddress,
  DeliveryTime,
  PaymentMethod,
  OrderSummary
} from "./components";
import { useRealtimeCart } from "@/context/realtime-cart-context";
import { useAuth } from "@/context/unified-auth-context";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ShoppingBag, Loader2 } from "lucide-react";

// Main checkout content component
const CheckoutContent: React.FC = () => {
  const { cart, sessionId, isProcessingOrder } = useRealtimeCart();
  const { user } = useAuth();
  const { currentStep, isFormComplete } = useCheckout();
  const [isInitiatingCheckout, setIsInitiatingCheckout] = useState(false);
  const [checkoutInitiated, setCheckoutInitiated] = useState(false);
  const [initiationError, setInitiationError] = useState<string | null>(null);

  // Validate checkout when component mounts and cart is not empty
  useEffect(() => {
    const validateCheckout = async () => {
      // Skip if already initiated, no cart, or no session/user
      if (checkoutInitiated || cart.length === 0 || (!user && !sessionId)) {
        return;
      }

      setIsInitiatingCheckout(true);
      setInitiationError(null);

      try {
        console.log('🚀 CHECKOUT: Validating checkout for cart with', cart.length, 'items');

        // Simple validation - just check that we have items and they're valid
        const itemsByBusiness = cart.reduce((acc, item) => {
          const businessId = item.businessId.toString();
          if (!acc[businessId]) {
            acc[businessId] = [];
          }
          acc[businessId].push(item);
          return acc;
        }, {} as Record<string, any[]>);

        const businessCount = Object.keys(itemsByBusiness).length;
        const totalItems = cart.length;

        console.log(`✅ CHECKOUT: Validation successful - ${businessCount} businesses, ${totalItems} items`);
        console.log('📝 CHECKOUT: No orders created during validation - orders will be created when Place Order is clicked');
        setCheckoutInitiated(true);

      } catch (error) {
        console.error('❌ CHECKOUT: Validation failed:', error);
        setInitiationError(error instanceof Error ? error.message : 'Failed to validate checkout');
      } finally {
        setIsInitiatingCheckout(false);
      }
    };

    validateCheckout();
  }, [cart.length, user, sessionId, checkoutInitiated]);

  // Return early if cart is empty
  if (cart.length === 0) {
    // Show loading screen if order is being processed
    if (isProcessingOrder) {
      return (
        <div className="container mx-auto px-4 py-8">
          <h1 className="text-2xl font-bold mb-6">Checkout</h1>
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <div className="text-center">
              <Loader2 className="h-12 w-12 mx-auto text-emerald-600 animate-spin mb-4" />
              <h2 className="text-xl font-semibold mb-2">Processing your order...</h2>
              <p className="text-gray-600">Please wait while we confirm your order</p>
            </div>
          </div>
        </div>
      );
    }

    // Show empty cart screen
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-6">Checkout</h1>
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="text-center">
            <ShoppingBag className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <h2 className="text-xl font-semibold mb-2">Your cart is empty</h2>
            <p className="text-gray-600 mb-6">Add some items to your cart to proceed with checkout</p>
            <Button asChild>
              <Link href="/businesses">Browse Businesses</Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Show loading state while initiating checkout
  if (isInitiatingCheckout) {
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-6">Checkout</h1>
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="text-center">
            <Loader2 className="h-8 w-8 mx-auto text-blue-600 animate-spin mb-4" />
            <h2 className="text-xl font-semibold mb-2">Preparing checkout...</h2>
            <p className="text-gray-600">Validating your cart</p>
          </div>
        </div>
      </div>
    );
  }

  // Show error state if initiation failed
  if (initiationError) {
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-6">Checkout</h1>
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="text-center">
            <div className="text-red-600 mb-4">❌</div>
            <h2 className="text-xl font-semibold mb-2">Checkout Error</h2>
            <p className="text-gray-600 mb-6">{initiationError}</p>
            <Button asChild>
              <Link href="/cart">Return to Cart</Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Checkout</h1>

      <CheckoutSteps />

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            {currentStep === 1 && (
              <>
                <h2 className="text-xl font-semibold mb-4">Customer Information</h2>
                <CustomerInformation />
              </>
            )}

            {currentStep === 2 && (
              <>
                <h2 className="text-xl font-semibold mb-4">Delivery Address</h2>
                <DeliveryAddress />
              </>
            )}

            {currentStep === 3 && (
              <>
                <h2 className="text-xl font-semibold mb-4">Delivery Time</h2>
                <DeliveryTime />
              </>
            )}

            {currentStep === 4 && (
              <>
                <h2 className="text-xl font-semibold mb-4">Payment Method</h2>
                <PaymentMethod />
              </>
            )}
          </div>
        </div>

        <div>
          <OrderSummary isFormComplete={isFormComplete} />
        </div>
      </div>
    </div>
  );
};

// Wrapper component with provider
export default function CheckoutPage() {
  return (
    <CheckoutProvider>
      <CheckoutContent />
    </CheckoutProvider>
  );
}
