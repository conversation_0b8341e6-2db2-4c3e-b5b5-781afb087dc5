"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { BusinessStatusBadge } from "./business-status-badge"
import { BusinessStatusUpdater } from "./business-status-updater"
import { Badge } from "@/components/ui/badge"
import { Store, ChevronDown, ChevronUp } from "lucide-react"
import { cn } from "@/lib/utils"

// Updated interface to work with orders directly instead of order_businesses
interface Order {
  id: number
  business_id: number
  business_name: string
  business_type: string
  status: string
  subtotal: number
  delivery_fee: number
  total: number
  order_number: string
}

interface MultiBusinessStatusProps {
  orders: Order[]
  isAdmin?: boolean
  onStatusChange?: (orderId: number, businessId: number, newStatus: string) => void
}

export function MultiBusinessStatusNew({
  orders,
  isAdmin = false,
  onStatusChange
}: MultiBusinessStatusProps) {
  const [expandedBusinesses, setExpandedBusinesses] = useState<number[]>([])

  const toggleBusiness = (businessId: number) => {
    setExpandedBusinesses(prev => 
      prev.includes(businessId)
        ? prev.filter(id => id !== businessId)
        : [...prev, businessId]
    )
  }

  const handleStatusChange = (orderId: number, businessId: number, newStatus: string) => {
    if (onStatusChange) {
      onStatusChange(orderId, businessId, newStatus)
    }
  }

  // If there's only one order, don't show the multi-business UI
  if (orders.length === 1) {
    const order = orders[0]
    
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Order Status</CardTitle>
        </CardHeader>
        <CardContent>
          {isAdmin && onStatusChange ? (
            <BusinessStatusUpdater
              orderId={order.id}
              businessId={order.business_id}
              businessName={order.business_name}
              currentStatus={order.status}
              onStatusChange={(newStatus) => handleStatusChange(order.id, order.business_id, newStatus)}
            />
          ) : (
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium">Status:</span>
              <BusinessStatusBadge status={order.status} />
            </div>
          )}
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Order Status</CardTitle>
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium">Orders:</span>
            <Badge variant="outline">{orders.length}</Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-sm text-muted-foreground">
          This order includes items from multiple businesses. Each business is processing their part of your order independently.
        </p>
        
        <div className="space-y-3">
          {orders.map((order) => (
            <div key={order.id} className="border rounded-md overflow-hidden">
              <div 
                className="flex items-center justify-between p-3 bg-gray-50 cursor-pointer"
                onClick={() => toggleBusiness(order.business_id)}
              >
                <div className="flex items-center space-x-2">
                  <Store className="h-4 w-4 text-gray-500" />
                  <span className="font-medium">{order.business_name}</span>
                  <Badge 
                    variant="outline" 
                    className={cn(
                      "ml-2 text-xs",
                      order.business_type === "restaurant" && "bg-orange-50 text-orange-700 border-orange-200",
                      order.business_type === "shop" && "bg-blue-50 text-blue-700 border-blue-200",
                      order.business_type === "cafe" && "bg-amber-50 text-amber-700 border-amber-200",
                      order.business_type === "pharmacy" && "bg-green-50 text-green-700 border-green-200"
                    )}
                  >
                    {order.business_type}
                  </Badge>
                </div>
                <div className="flex items-center space-x-3">
                  <BusinessStatusBadge status={order.status} size="sm" />
                  {expandedBusinesses.includes(order.business_id) ? (
                    <ChevronUp className="h-4 w-4 text-gray-500" />
                  ) : (
                    <ChevronDown className="h-4 w-4 text-gray-500" />
                  )}
                </div>
              </div>
              
              {expandedBusinesses.includes(order.business_id) && (
                <div className="p-3 border-t">
                  {isAdmin && onStatusChange ? (
                    <BusinessStatusUpdater
                      orderId={order.id}
                      businessId={order.business_id}
                      businessName={order.business_name}
                      currentStatus={order.status}
                      onStatusChange={(newStatus) => handleStatusChange(order.id, order.business_id, newStatus)}
                    />
                  ) : (
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium">Status:</span>
                        <BusinessStatusBadge status={order.status} />
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {getStatusDescription(order.status, order.business_name)}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Order #: {order.order_number}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

function getStatusDescription(status: string, businessName: string): string {
  switch (status) {
    case "pending":
      return `${businessName} has received your order and will confirm it shortly.`
    case "confirmed":
      return `${businessName} has confirmed your order.`
    case "preparing":
      return `${businessName} is preparing your order.`
    case "ready":
      return `Your order is ready at ${businessName}.`
    case "out_for_delivery":
      return `Your order from ${businessName} is out for delivery.`
    case "delivered":
      return `Your order from ${businessName} has been delivered.`
    case "cancelled":
      return `Your order from ${businessName} has been cancelled.`
    default:
      return `Your order status is ${status}.`
  }
}
