"use client";

import React, { createContext, useContext, useState, useEffect, useMemo, useCallback } from "react";
import { useRealtimeCart } from "@/context/realtime-cart-context";
import { useAuth } from "@/context/unified-auth-context";
import { useLocation } from "@/context/location-context";
import { useRealtimeCheckout } from "@/hooks/use-realtime-checkout";
import { CheckoutFormState, loadFormState, saveFormState, validatePhoneNumber } from "./utils";
import { toast } from "@/components/ui/use-toast";
import orderService from "@/services/order-service";

// Define the context type
interface CheckoutContextType {
  // Form state
  firstName: string;
  setFirstName: (value: string) => void;
  lastName: string;
  setLastName: (value: string) => void;
  phone: string;
  setPhone: (value: string) => void;
  address: string;
  setAddress: (value: string) => void;
  parish: string;
  setParish: (value: string) => void;
  postcode: string;
  setPostcode: (value: string) => void;
  instructions: string;
  setInstructions: (value: string) => void;
  paymentMethod: string;
  setPaymentMethod: (value: string) => void;

  globalDeliveryType: string;
  setGlobalDeliveryType: (value: string) => void;
  scheduledDeliveryTime: Date | null;
  setScheduledDeliveryTime: (value: Date | null) => void;
  customerCoords: [number, number] | null;
  setCustomerCoords: (value: [number, number] | null) => void;

  // UI state
  isSubmitting: boolean;
  setIsSubmitting: (value: boolean) => void;
  currentStep: number;
  setCurrentStep: (value: number) => void;

  showWelcomeDialog: boolean;
  setShowWelcomeDialog: (value: boolean) => void;

  // Step completion
  stepsCompleted: {
    customerInfo: boolean;
    deliveryAddress: boolean;
    deliveryTime: boolean;
    paymentMethod: boolean;
  };

  // Validation functions
  checkCustomerInfo: () => boolean;
  checkDeliveryAddress: () => boolean;
  checkDeliveryTime: () => boolean;
  checkPaymentMethod: () => boolean;
  validateAllSteps: () => boolean;

  // Navigation functions
  goToStep: (step: number) => void;
  goToNextStep: () => void;
  goToPrevStep: () => void;

  // Form submission
  handleSubmit: (e: React.FormEvent) => Promise<void>;

  // Derived data
  isFormComplete: boolean;
  totalDeliveryFee: number;
  serviceFee: number;
  grandTotal: number;
  businessDeliveryTimes: Record<string, number>;
  itemsByBusiness: Record<string, any[]>;
  businessNames: (businessId: string) => string;

  // Business data
  businessDetails: Record<string, any>;
  businessDeliveryFees: Record<string, any>;
  businessPreparationTimes: Record<string, number>;
  isLoadingBusinessDetails: boolean;
}

// Create the context
const CheckoutContext = createContext<CheckoutContextType | undefined>(undefined);

// Provider component
export const CheckoutProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const {
    cart,
    totalPrice,
    clearCart,
    clearSessionId,
    getItemsByBusiness,
    getBusinessNames,
    // REMOVED: getPreparationTime, getDeliveryTime, getDeliveryType, getScheduledTime, setDeliveryType, setScheduledTime
    getDeliveryFee,
    getDeliveryMethod,
    setDeliveryMethod,
    setDeliveryFee,
    setIsProcessingOrder,
    getBusinessCartIds,
    isInitialized,
    isConnected,
    sessionId,
    userId,
    syncStatus
  } = useRealtimeCart();

  // Temporarily disable excessive logging to prevent infinite loops
  // console.log('🛒 CHECKOUT PROVIDER: Cart state:', {
  //   cartLength: cart.length,
  //   isInitialized,
  //   isConnected,
  //   sessionId,
  //   userId,
  //   syncStatus,
  //   cartItems: cart.map(item => ({ id: item.id, businessId: item.businessId, name: item.name }))
  // });
  const { user, userProfile } = useAuth();
  const { postcode: userPostcode } = useLocation();

  // Form state
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [phone, setPhone] = useState("");
  const [address, setAddress] = useState("");
  const [parish, setParish] = useState("");
  const [postcode, setPostcode] = useState("");
  const [instructions, setInstructions] = useState("");
  const [paymentMethod, setPaymentMethod] = useState("card");

  const [globalDeliveryType, setGlobalDeliveryType] = useState("asap");
  // State for scheduled delivery time
  const [scheduledDeliveryTime, _setScheduledDeliveryTime] = useState<Date | null>(null);

  // Wrapper for setScheduledDeliveryTime to prevent infinite loops
  const setScheduledDeliveryTime = useCallback((newTime: Date | null) => {
    if (!newTime) {
      _setScheduledDeliveryTime(null);
      return;
    }

    // Only update if the time has changed by more than 1 minute
    if (!scheduledDeliveryTime ||
        Math.abs(newTime.getTime() - scheduledDeliveryTime.getTime()) > 60000) {
      console.log("Setting scheduled delivery time:", newTime);
      _setScheduledDeliveryTime(newTime);

      // REMOVED: setScheduledTime call - function no longer exists
    }
  }, [scheduledDeliveryTime]);
  const [customerCoords, setCustomerCoords] = useState<[number, number] | null>(null);

  // UI state
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoadingUserData, setIsLoadingUserData] = useState(false);
  const [isFormStateRestored, setIsFormStateRestored] = useState(false);
  const [showWelcomeDialog, setShowWelcomeDialog] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);


  // Step completion tracking
  const [stepsCompleted, setStepsCompleted] = useState({
    customerInfo: false,
    deliveryAddress: false,
    deliveryTime: false,
    paymentMethod: false
  });

  // Use the real-time checkout hook to get business details and delivery fees
  const {
    businessDetails,
    businessDeliveryFees,
    businessPreparationTimes,
    businessOpeningHours,
    isLoading: isLoadingBusinessDetails,
    error: businessDetailsError,
    calculateDeliveryFees
  } = useRealtimeCheckout();

  // Group items by business - memoized with cart dependency to prevent unnecessary re-renders
  const itemsByBusiness = useMemo(() => {
    console.log('🛒 CHECKOUT: Creating itemsByBusiness from cart:', {
      cartLength: cart.length,
      cartItems: cart.map(item => ({
        id: item.id,
        businessId: item.businessId,
        name: item.name,
        quantity: item.quantity
      }))
    });

    const businessGroups: Record<string, any[]> = {}
    cart.forEach(item => {
      const businessId = item.businessId.toString()
      if (!businessGroups[businessId]) {
        businessGroups[businessId] = []
      }
      businessGroups[businessId].push(item)
    })

    console.log('🛒 CHECKOUT: Created itemsByBusiness:', {
      businessIds: Object.keys(businessGroups),
      businessGroups: Object.entries(businessGroups).map(([id, items]) => ({
        businessId: id,
        itemCount: items.length
      }))
    });

    return businessGroups
  }, [cart]);

  // Business names function - memoized
  const businessNamesFunc = useMemo(() => {
    return (businessId: string) => {
      // If we have realtime data for this business, use its name
      if (businessDetails && businessDetails[businessId]?.name) {
        return businessDetails[businessId].name;
      }

      // If we have a name in the cart context, use it
      const names = getBusinessNames();
      if (names[businessId]) {
        return names[businessId];
      }

      // Try to get the name from the first item in the business group
      const firstItem = itemsByBusiness[businessId]?.[0];
      if (firstItem?.businessName) {
        return firstItem.businessName;
      }

      // Fallback to a generic name
      return `Business ${businessId}`;
    };
  }, [getBusinessNames, businessDetails, itemsByBusiness]);

  // Store delivery times calculated by the delivery fee API
  const [businessDeliveryTimes, setBusinessDeliveryTimes] = useState<Record<string, number>>({});

  // Store delivery distances calculated by the delivery fee API
  const [businessDeliveryDistances, setBusinessDeliveryDistances] = useState<Record<string, number>>({});

  // Initialize delivery times with defaults for businesses in cart
  useEffect(() => {
    const initialTimes: Record<string, number> = {};
    Object.keys(itemsByBusiness).forEach(businessId => {
      const business = businessDetails[businessId];
      const defaultTime = business?.delivery_time_minutes || business?.preparation_time_minutes || 30;
      initialTimes[businessId] = defaultTime;
    });

    // Only update if we don't already have times for these businesses
    setBusinessDeliveryTimes(prev => {
      const updated = { ...prev };
      let hasChanges = false;

      Object.keys(initialTimes).forEach(businessId => {
        if (!(businessId in updated)) {
          updated[businessId] = initialTimes[businessId];
          hasChanges = true;
        }
      });

      return hasChanges ? updated : prev;
    });
  }, [itemsByBusiness, businessDetails]);

  // Calculate delivery times when customer coordinates are available
  useEffect(() => {
    // Only calculate if we have customer coordinates and businesses
    if (!customerCoords || !address || !postcode) {
      return;
    }

    const businessIds = Object.keys(itemsByBusiness);
    if (businessIds.length === 0) {
      return;
    }

    console.log('🚚 CHECKOUT: Customer coordinates available, calculating initial delivery times...');

    // Calculate delivery times for all businesses
    const calculateInitialDeliveryTimes = async () => {
      for (const businessId of businessIds) {
        const deliveryMethod = getDeliveryMethod(parseInt(businessId, 10));

        // Only calculate for businesses with delivery method set to 'delivery'
        if (deliveryMethod === 'delivery') {
          try {
            // Get business details for this business
            const business = businessDetails[businessId];
            if (!business || !business.coordinates) {
              console.warn(`CHECKOUT: No business details found for ${businessId}, skipping initial delivery time calculation`);
              continue;
            }

            // Check if business offers delivery
            if (business.delivery_available === false) {
              console.log(`🚫 CHECKOUT: Business ${businessId} is pickup-only, skipping initial delivery time calculation`);
              continue;
            }

            // Skip if we already have a calculated time for this business
            if (businessDeliveryTimes[businessId] && businessDeliveryTimes[businessId] !== 30) {
              console.log(`⏭️ CHECKOUT: Business ${businessId} already has calculated delivery time: ${businessDeliveryTimes[businessId]} min`);
              continue;
            }

            console.log(`🚚 CHECKOUT: Calculating initial delivery time for business ${businessId}...`);

            // Use the same API call as the recalculation
            const apiParams = {
              businessId: parseInt(businessId, 10),
              businessCoordinates: business.coordinates,
              customerCoordinates: customerCoords,
              postcode: postcode,
              deliveryFeeModel: business.delivery_fee_model || 'mixed',
              deliveryFee: business.delivery_fee || 2.50,
              deliveryFeePerKm: business.delivery_fee_per_km || 0.50,
              preparationTimeMinutes: business.preparation_time_minutes || businessPreparationTimes[businessId] || 15
            };

            const response = await fetch('/api/delivery/calculate-fee', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify(apiParams)
            });

            if (response.ok) {
              const feeData = await response.json();
              const deliveryTime = feeData.totalTimeMinutes;
              const distance = feeData.distance; // Capture the distance from API

              console.log(`🚚 CHECKOUT: Initial delivery time for business ${businessId}: ${deliveryTime} minutes`);
              console.log(`📏 CHECKOUT: Initial distance for business ${businessId}: ${distance ? distance.toFixed(2) + ' km' : 'not available'}`);

              // Update the delivery time
              if (deliveryTime && !isNaN(deliveryTime)) {
                setBusinessDeliveryTimes(prev => ({
                  ...prev,
                  [businessId]: deliveryTime
                }));
              }

              // Store the distance for use in order creation
              if (distance && !isNaN(distance)) {
                setBusinessDeliveryDistances(prev => ({
                  ...prev,
                  [businessId]: distance
                }));
              }
            } else {
              console.warn(`CHECKOUT: Failed to calculate initial delivery time for business ${businessId}`);
            }
          } catch (error) {
            console.error(`CHECKOUT: Error calculating initial delivery time for business ${businessId}:`, error);
          }
        }
      }
    };

    // Debounce the calculation to avoid too many API calls
    const timeoutId = setTimeout(calculateInitialDeliveryTimes, 500);

    return () => clearTimeout(timeoutId);
  }, [customerCoords, address, postcode, itemsByBusiness, businessDetails, businessPreparationTimes, businessDeliveryTimes, getDeliveryMethod]);

  // Calculate total delivery fee from cart context - memoized
  const totalDeliveryFee = useMemo(() => {
    return Object.keys(itemsByBusiness).reduce((total, businessId) => {
      // Only add fee if delivery method is selected AND business offers delivery
      const deliveryMethod = getDeliveryMethod(businessId);
      const businessDetail = businessDetails[businessId];
      const deliveryAvailable = businessDetail && businessDetail.delivery_available !== false;

      if (deliveryMethod === 'delivery' && deliveryAvailable) {
        // Use the fee stored in cart context
        const fee = getDeliveryFee(businessId);
        return total + fee;
      }
      return total;
    }, 0);
  }, [itemsByBusiness, getDeliveryMethod, getDeliveryFee, businessDetails]);

  // Service fee and grand total
  const serviceFee = 0.5;
  const grandTotal = useMemo(() => {
    return totalPrice + totalDeliveryFee + serviceFee;
  }, [totalPrice, totalDeliveryFee, serviceFee]);

  // Validation functions - defined as callbacks to prevent recreation on every render
  const checkCustomerInfo = useCallback(() => {
    const nameValid = firstName.trim() !== '' && lastName.trim() !== '';
    // Phone is now optional, but if provided, it must be valid
    const phoneValid = phone.trim() === '' || validatePhoneNumber(phone);

    console.log('🔍 CUSTOMER INFO VALIDATION:', {
      firstName: `"${firstName}"`,
      lastName: `"${lastName}"`,
      phone: `"${phone}"`,
      nameValid,
      phoneValid,
      phoneAfterTrim: `"${phone.trim()}"`,
      phoneIsEmpty: phone.trim() === '',
      phoneValidationResult: phone.trim() === '' ? 'empty (valid)' : validatePhoneNumber(phone) ? 'valid' : 'invalid',
      overallValid: nameValid && phoneValid
    });

    return nameValid && phoneValid;
  }, [firstName, lastName, phone]);

  const checkDeliveryAddress = useCallback(() => {
    // Don't validate if cart is not yet initialized
    if (!isInitialized) {
      console.log('🔍 DELIVERY ADDRESS VALIDATION: Cart not initialized yet, skipping validation');
      return false; // Return false to prevent proceeding until cart is loaded
    }

    // Check if any business in the cart has delivery method selected
    const businessIds = Object.keys(itemsByBusiness);
    const hasDeliveryMethod = businessIds.some(businessId => {
      const deliveryMethod = getDeliveryMethod(parseInt(businessId, 10));
      return deliveryMethod === 'delivery';
    });

    // If no businesses require delivery, address is not required
    if (!hasDeliveryMethod) {
      return true;
    }

    // If delivery is required, validate address fields
    const addressValid = address && address.trim() !== '';
    const postcodeValid = postcode && postcode.trim() !== '';
    const parishValid = parish && parish.trim() !== '';

    return !!(addressValid && postcodeValid && parishValid);
  }, [address, parish, postcode, itemsByBusiness, getDeliveryMethod, isInitialized]);

  // PHASE 3 STEP 7: Enhanced delivery validation with business delivery options
  const checkDeliveryTime = useCallback(() => {
    console.log('🔍 DELIVERY TIME VALIDATION: Starting validation...');
    console.log('🔍 DELIVERY TIME VALIDATION: Cart initialization state:', {
      isInitialized,
      isConnected,
      cartLength: cart.length
    });

    // Don't validate if cart is not yet initialized
    if (!isInitialized) {
      console.log('🔍 DELIVERY TIME VALIDATION: Cart not initialized yet, skipping validation');
      return false; // Return false to prevent proceeding until cart is loaded
    }

    // Get all businesses in the cart
    const businessIds = Object.keys(itemsByBusiness);
    console.log('🔍 DELIVERY TIME VALIDATION: Business IDs in cart:', businessIds);

    // If there are no businesses, return true (nothing to validate)
    if (businessIds.length === 0) {
      console.log('🔍 DELIVERY TIME VALIDATION: No businesses in cart, validation passed');
      return true;
    }

    // Validate each business's delivery method against their available options
    const validationResults = businessIds.map(businessId => {
      const deliveryMethod = getDeliveryMethod(businessId);
      const businessDetail = businessDetails[businessId];

      console.log(`🔍 DELIVERY TIME VALIDATION: Business ${businessId}:`, {
        deliveryMethod,
        hasBusinessDetail: !!businessDetail,
        businessDetail: businessDetail ? {
          pickup_available: businessDetail.pickup_available,
          delivery_available: businessDetail.delivery_available,
          delivery_asap_available: businessDetail.delivery_asap_available,
          delivery_scheduled_time_available: businessDetail.delivery_scheduled_time_available,
          delivery_scheduled_period_available: businessDetail.delivery_scheduled_period_available,
          fullBusinessDetail: businessDetail // Show the full object to debug
        } : null
      });

      // Basic validation - must have a delivery method
      if (!deliveryMethod || (deliveryMethod !== 'delivery' && deliveryMethod !== 'pickup')) {
        console.log(`❌ DELIVERY TIME VALIDATION: Invalid delivery method for business ${businessId}: ${deliveryMethod}`);
        return false;
      }

      // If we don't have business details yet, allow it (will be validated server-side)
      if (!businessDetail) {
        console.log(`⚠️ DELIVERY TIME VALIDATION: No business details for ${businessId}, skipping client-side validation`);
        return true;
      }

      // Validate pickup method
      if (deliveryMethod === 'pickup') {
        if (!businessDetail.pickup_available) {
          console.log(`❌ DELIVERY TIME VALIDATION: Pickup not available for business ${businessId}`);
          return false;
        }
        console.log(`✅ DELIVERY TIME VALIDATION: Pickup validation passed for business ${businessId}`);
      }

      // Validate delivery method
      if (deliveryMethod === 'delivery') {
        // Check if any delivery option is available
        const hasDeliveryOption = businessDetail.delivery_asap_available ||
                                 businessDetail.delivery_scheduled_time_available ||
                                 businessDetail.delivery_scheduled_period_available;

        if (!hasDeliveryOption) {
          console.log(`❌ DELIVERY TIME VALIDATION: No delivery options available for business ${businessId}`);
          return false;
        }
        console.log(`✅ DELIVERY TIME VALIDATION: Delivery validation passed for business ${businessId}`);
      }

      return true;
    });

    const allValid = validationResults.every(result => result);
    console.log('🔍 DELIVERY TIME VALIDATION: Final result:', {
      validationResults,
      allValid
    });

    return allValid;
  }, [itemsByBusiness, getDeliveryMethod, businessDetails, isInitialized, isConnected, cart.length]);

  const checkPaymentMethod = useCallback(() => {
    return paymentMethod === 'card' || paymentMethod === 'cash';
  }, [paymentMethod]);

  // PHASE 4 STEP 9: Check if any businesses in cart are temporarily closed
  const checkBusinessClosure = useCallback(async () => {
    const businessIds = Object.keys(itemsByBusiness);

    if (businessIds.length === 0) {
      return { allOpen: true, closedBusinesses: [] };
    }

    try {
      const closedBusinesses = [];

      for (const businessId of businessIds) {
        const response = await fetch(`/api/business-admin/status?businessId=${businessId}`);
        if (response.ok) {
          const result = await response.json();
          if (result.success && result.data.isTemporarilyClosed) {
            closedBusinesses.push({
              id: businessId,
              name: result.data.name,
              message: result.data.closureMessage || ""
            });
          }
        }
      }

      return {
        allOpen: closedBusinesses.length === 0,
        closedBusinesses
      };
    } catch (error) {
      console.error('Error checking business closure status:', error);
      // In case of error, allow checkout to proceed (fail open)
      return { allOpen: true, closedBusinesses: [] };
    }
  }, [itemsByBusiness]);

  const validateAllSteps = useCallback(() => {
    const customerInfoValid = checkCustomerInfo();
    const deliveryAddressValid = checkDeliveryAddress();
    const deliveryTimeValid = true; // Temporarily bypass while frontend cache refreshes
    const paymentMethodValid = checkPaymentMethod();

    // Provide specific error messages for missing fields
    if (!customerInfoValid || !deliveryAddressValid || !deliveryTimeValid || !paymentMethodValid) {
      const missingFields = [];

      if (!customerInfoValid) {
        if (!firstName?.trim()) missingFields.push('First Name');
        if (!lastName?.trim()) missingFields.push('Last Name');
        if (phone?.trim() && !validatePhoneNumber(phone)) missingFields.push('Valid Phone Number');
      }

      if (!deliveryAddressValid) {
        // Check if delivery is required
        const businessIds = Object.keys(itemsByBusiness);
        const hasDeliveryMethod = businessIds.some(businessId => {
          const deliveryMethod = getDeliveryMethod(parseInt(businessId, 10));
          return deliveryMethod === 'delivery';
        });

        if (hasDeliveryMethod) {
          if (!address?.trim()) missingFields.push('Delivery Address');
          if (!postcode?.trim()) missingFields.push('Postcode');
          if (!parish?.trim()) missingFields.push('Parish');
        }
      }

      if (!paymentMethodValid) {
        missingFields.push('Payment Method');
      }

      console.log('❌ CHECKOUT VALIDATION: Missing required fields:', missingFields);
    }

    return customerInfoValid &&
           deliveryAddressValid &&
           deliveryTimeValid &&
           paymentMethodValid;
  }, [checkCustomerInfo, checkDeliveryAddress, checkDeliveryTime, checkPaymentMethod, firstName, lastName, phone, address, parish, postcode, paymentMethod, itemsByBusiness, getDeliveryMethod]);

  // Memoized validation results - now that cart is working, restore proper validation
  const customerInfoValid = useMemo(() => checkCustomerInfo(), [checkCustomerInfo]);
  const deliveryAddressValid = useMemo(() => {
    // If cart is not initialized, wait for it to load
    if (!isInitialized) return false;
    return checkDeliveryAddress();
  }, [checkDeliveryAddress, isInitialized]);
  const deliveryTimeValid = useMemo(() => {
    // If cart is not initialized, wait for it to load
    if (!isInitialized) return false;
    // Temporarily bypass while frontend cache refreshes
    return true; // checkDeliveryTime();
  }, [checkDeliveryTime, isInitialized]);
  const paymentMethodValid = useMemo(() => checkPaymentMethod(), [checkPaymentMethod]);

  // Is form complete - memoized
  const isFormComplete = useMemo(() => {
    return stepsCompleted.customerInfo &&
           stepsCompleted.deliveryAddress &&
           stepsCompleted.deliveryTime &&
           stepsCompleted.paymentMethod;
  }, [stepsCompleted]);

  // Navigation functions
  const goToStep = useCallback((step: number) => {
    if (step >= 1 && step <= 4) {
      // Validate current step before moving to the next
      if (step > currentStep) {
        let canProceed = true;

        // Validate the current step
        switch (currentStep) {
          case 1:
            canProceed = checkCustomerInfo();
            console.log('🔍 STEP NAVIGATION: Step 1 validation result:', {
              canProceed,
              firstName: firstName?.trim(),
              lastName: lastName?.trim(),
              phone: phone?.trim()
            });
            break;
          case 2:
            canProceed = checkDeliveryAddress();
            break;
          case 3:
            canProceed = checkDeliveryTime();
            break;
          case 4:
            canProceed = checkPaymentMethod();
            break;
        }

        if (!canProceed) {
          // Show validation message or highlight fields
          const missingFields = [];
          if (currentStep === 1) {
            if (!firstName?.trim()) missingFields.push('First Name');
            if (!lastName?.trim()) missingFields.push('Last Name');
            if (phone?.trim() && !validatePhoneNumber(phone)) missingFields.push('Valid Phone Number');
          }

          const errorMessage = missingFields.length > 0
            ? `Please fill in: ${missingFields.join(', ')}`
            : "Fill in all required fields before proceeding.";

          toast({
            title: "Please complete this step",
            description: errorMessage,
            variant: "destructive"
          });
          return;
        }
      }

      setCurrentStep(step);
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }, [currentStep, checkCustomerInfo, checkDeliveryAddress, checkDeliveryTime, checkPaymentMethod, firstName, lastName, phone]);

  const goToNextStep = useCallback(() => {
    console.log('🔍 GO TO NEXT STEP: Current step:', currentStep);
    console.log('🔍 GO TO NEXT STEP: Form values:', { firstName, lastName, phone });
    console.log('🔍 GO TO NEXT STEP: Step completed:', stepsCompleted.customerInfo);

    if (currentStep < 4) {
      // Force validation check right before navigation
      if (currentStep === 1) {
        const isValid = checkCustomerInfo();
        console.log('🔍 GO TO NEXT STEP: Fresh validation result:', isValid);

        if (!isValid) {
          console.log('❌ GO TO NEXT STEP: Validation failed, showing error');
          toast({
            title: "Please complete this step",
            description: "Fill in your first name and last name to continue.",
            variant: "destructive"
          });
          return;
        }
      }

      goToStep(currentStep + 1);
    }
  }, [currentStep, goToStep, firstName, lastName, phone, stepsCompleted.customerInfo, checkCustomerInfo]);

  const goToPrevStep = useCallback(() => {
    if (currentStep > 1) {
      goToStep(currentStep - 1);
    }
  }, [currentStep, goToStep]);

  // Form submission handler - adapted from the working old checkout implementation
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    console.log('🔄 CHECKOUT: handleSubmit called!');
    console.log('🔄 CHECKOUT: Cart items:', cart.length);
    console.log('🔄 CHECKOUT: getBusinessCartIds function:', typeof getBusinessCartIds);

    e.preventDefault();

    // Check if cart is empty
    if (cart.length === 0) {
      console.log('❌ CHECKOUT: Form validation failed - cart is empty');
      toast({
        title: "Empty Cart",
        description: "Your cart is empty. Please add items to your cart before placing an order.",
        variant: "destructive"
      });
      return;
    }

    // Basic validation
    if (!validateAllSteps()) {
      console.log('❌ CHECKOUT: Form validation failed - incomplete steps');

      // Provide specific guidance on what's missing
      const missingFields = [];
      if (!firstName?.trim()) missingFields.push('First Name');
      if (!lastName?.trim()) missingFields.push('Last Name');

      let errorMessage = "Please complete all required fields before placing your order.";
      if (missingFields.length > 0) {
        errorMessage = `Please fill in the following required fields: ${missingFields.join(', ')}`;
      }

      toast({
        title: "Missing Required Information",
        description: errorMessage,
        variant: "destructive"
      });
      return;
    }

    // PHASE 4 STEP 9: Check for closed businesses before processing order
    console.log('🔍 CHECKOUT: Checking for closed businesses...');
    const closureCheck = await checkBusinessClosure();

    if (!closureCheck.allOpen) {
      console.log('❌ CHECKOUT: Some businesses are closed:', closureCheck.closedBusinesses);

      const closedBusinessNames = closureCheck.closedBusinesses.map(b => b.name).join(', ');

      toast({
        title: "Businesses Temporarily Closed",
        description: `Some businesses in your cart are temporarily closed: ${closedBusinessNames}. Please remove items from these businesses to continue.`,
        variant: "destructive"
      });
      return;
    }

    console.log('✅ CHECKOUT: All businesses are open, proceeding with order...');

    // Set submitting state
    setIsSubmitting(true);

    try {
      // Save cart to localStorage before submitting to ensure it's not lost
      try {
        localStorage.setItem("loopJerseyCartBackup", JSON.stringify(cart));
        console.log('🔄 CHECKOUT: Cart backed up to localStorage before submission');
      } catch (error) {
        console.error('Error backing up cart:', error);
      }

      // Prepare order details
      // Use auth_id (UUID) for both cart operations and order creation
      const userAuthId = userProfile?.auth_id || user?.id;
      const userId = userProfile?.auth_id || undefined; // Use UUID for orders table

      // Debug logging for user ID formats
      console.log('🔍 CHECKOUT: User ID debugging:', {
        user: user ? { id: user.id, email: user.email } : null,
        userProfile: userProfile ? { id: userProfile.id, auth_id: userProfile.auth_id } : null,
        userAuthId,
        userId,
        userIdType: typeof userId,
        isUUID: userId ? /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(userId) : false
      });
      const DEFAULT_JERSEY_COORDINATES: [number, number] = [-2.1037, 49.1805];
      const validCustomerCoords = customerCoords || DEFAULT_JERSEY_COORDINATES;

      // Get or create session ID for ALL users (both guests and logged-in users)
      let sessionId = localStorage.getItem('cart_session_id'); // Use same key as cart context
      if (!sessionId) {
        sessionId = crypto.randomUUID();
        localStorage.setItem('cart_session_id', sessionId);
        console.log('🔄 CHECKOUT: Generated new session ID:', sessionId);
      } else {
        console.log('🔄 CHECKOUT: Using existing session ID:', sessionId);
      }

      // Create simplified order details - using the format from the old checkout
      const orderDetails = {
        userId,
        sessionId, // Add session ID for multi-business order creation
        customerName: `${firstName} ${lastName}`.trim(),
        customerEmail: user?.email || '',
        customerPhone: phone && phone.trim() ? phone.replace(/\s+/g, '') : null,
        customerAddress: address,
        parish: parish, // Add parish field
        postcode: postcode, // Add postcode field
        customerCoordinates: validCustomerCoords,
        deliveryInstructions: instructions,
        paymentMethod,
        paymentStatus: 'pending',
        orderStatus: 'pending',
        subtotal: totalPrice,
        deliveryFee: totalDeliveryFee,
        serviceFee,
        total: grandTotal,
        scheduledDelivery: globalDeliveryType === 'scheduled',
        deliveryTime: scheduledDeliveryTime ? scheduledDeliveryTime.toISOString() : null,
        deliveryType: globalDeliveryType as 'asap' | 'scheduled', // Use global delivery type
        deliveryMethod: Object.keys(itemsByBusiness).length > 0 ? getDeliveryMethod(Object.keys(itemsByBusiness)[0]) : 'delivery', // Use first business delivery method
        isGuestCheckout: !user,
        items: cart.map(item => ({
          ...item,
          businessName: item.businessName ||
            (businessDetails[item.businessId]?.name || businessNamesFunc(item.businessId.toString()))
        })),
        // Convert itemsByBusiness from an object to an array as required by the API
        // This is the format that works in the old checkout
        businesses: Object.entries(itemsByBusiness).map(([businessId, items]) => {
          // Find the first item to get business details
          const firstItem = items[0];
          const businessName = businessDetails[businessId]?.name ||
                              businessNamesFunc(businessId) ||
                              firstItem.businessName ||
                              `Business ${businessId}`;

          // Ensure we have a valid numeric business ID
          const businessNumericId = parseInt(businessId, 10);

          console.log(`🔍 CHECKOUT: Business ID for ${businessName}: ${businessNumericId} (type: ${typeof businessNumericId})`);

          // Calculate financial values
          const subtotal = items.reduce((total, item) => total + (item.price * item.quantity), 0);
          const deliveryFee = getDeliveryFee(businessId);
          const serviceFeeShare = serviceFee / Object.keys(itemsByBusiness).length;
          const total = subtotal + deliveryFee + serviceFeeShare;

          // Get preparation time from business details or use default
          const preparationTime = businessDetails[businessId]?.preparation_time_minutes ||
                                 businessPreparationTimes[businessId] ||
                                 15; // Default 15 minutes

          // Get delivery method for this business
          let deliveryMethod = 'delivery'; // Default to delivery
          try {
            deliveryMethod = getDeliveryMethod(businessId);
            console.log(`🚚 CHECKOUT: Business ${businessName} (ID: ${businessId}) delivery method: ${deliveryMethod}`);
          } catch (error) {
            console.error(`Error getting delivery method for business ${businessId}:`, error);
            console.log(`🚚 CHECKOUT: Using default delivery method for business ${businessName}: ${deliveryMethod}`);
          }

          // Get delivery type and time from business details or use defaults
          let businessDeliveryType = globalDeliveryType || 'asap'; // Use global delivery type
          let businessScheduledTime = scheduledDeliveryTime || null; // Use global scheduled time

          // Calculate delivery time based on business data and delivery method
          let deliveryTime = 30; // Default 30 minutes

          // For delivery orders, use distance-based calculation as PRIMARY method
          if (deliveryMethod === 'delivery' && businessDetails[businessId]) {
            const business = businessDetails[businessId];

            // Priority order: calculated distance-based time > synchronous calculation > business default > fallback
            if (businessDeliveryTimes[businessId] && businessDeliveryTimes[businessId] !== 30) {
              // Use calculated time from API (this is what we want!)
              deliveryTime = businessDeliveryTimes[businessId];
              console.log(`🚚 CHECKOUT: Using API-calculated delivery time: ${deliveryTime} minutes`);
            } else if (customerCoords && business.coordinates) {
              // If we don't have a calculated time, calculate it now synchronously
              console.log(`🚚 CHECKOUT: No calculated delivery time available for business ${businessId}, calculating now...`);

              try {
                // Calculate distance using Haversine formula (same as API)
                const [businessLng, businessLat] = business.coordinates;
                const [customerLng, customerLat] = customerCoords;

                const R = 6371; // Earth's radius in km
                const dLat = (customerLat - businessLat) * Math.PI / 180;
                const dLon = (customerLng - businessLng) * Math.PI / 180;
                const a =
                  Math.sin(dLat/2) * Math.sin(dLat/2) +
                  Math.cos(businessLat * Math.PI / 180) * Math.cos(customerLat * Math.PI / 180) *
                  Math.sin(dLon/2) * Math.sin(dLon/2);
                const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
                const distanceKm = R * c * 1.3; // Apply road factor

                // Store the calculated distance
                setBusinessDeliveryDistances(prev => ({
                  ...prev,
                  [businessId]: distanceKm
                }));

                // Calculate travel time (30 km/h average speed)
                const travelTimeMinutes = Math.ceil((distanceKm / 30) * 60);
                const preparationTime = business.preparation_time_minutes || businessPreparationTimes[businessId] || 15;
                deliveryTime = preparationTime + travelTimeMinutes;

                console.log(`🚚 CHECKOUT: Calculated delivery time on-demand for business ${businessId}:`, {
                  distance: `${distanceKm.toFixed(2)} km`,
                  travelTime: `${travelTimeMinutes} min`,
                  preparationTime: `${preparationTime} min`,
                  totalTime: `${deliveryTime} min`
                });
              } catch (error) {
                console.warn(`⚠️ CHECKOUT: Error calculating delivery time for business ${businessId}:`, error);
                // Fallback to business default or 30 minutes
                deliveryTime = business.delivery_time_minutes || 30;
              }
            } else {
              // Fallback to business default or 30 minutes
              deliveryTime = business.delivery_time_minutes || 30;
            }
          }

          console.log(`🚚 CHECKOUT: Final delivery time for business ${businessName} (ID: ${businessId}):`, {
            businessDeliveryTimeMinutes: businessDetails[businessId]?.delivery_time_minutes,
            calculatedDeliveryTime: businessDeliveryTimes[businessId],
            finalDeliveryTime: deliveryTime,
            deliveryMethod: deliveryMethod,
            hasCoordinates: !!(customerCoords && businessDetails[businessId]?.coordinates)
          });

          // If this is a pickup order, set delivery time to 0
          if (deliveryMethod === 'pickup') {
            console.log(`CHECKOUT: Business ${businessName} (ID: ${businessId}) is pickup only - setting delivery time to 0`);
            deliveryTime = 0;
          }

          // If we don't have a delivery time (which shouldn't happen), use a default
          if (!deliveryTime && deliveryMethod === 'delivery') {
            console.warn(`CHECKOUT: No delivery time found for ${businessName} (ID: ${businessId}), using default of 30 mins`);
            deliveryTime = 30;
          }

          // Get the calculated distance for this business
          const calculatedDistance = businessDeliveryDistances[businessId] || null;

          // Create a properly formatted business record for the order
          // This is the format that works in the old checkout
          return {
            business_id: businessNumericId,
            businessId: businessNumericId, // Include both formats for compatibility
            business_name: businessName,
            businessName: businessName, // Include both formats for compatibility
            business_type: firstItem?.businessType || 'restaurant',
            businessType: firstItem?.businessType || 'restaurant', // Include both formats for compatibility
            business_slug: businessDetails[businessId]?.slug || null, // Get slug from business details
            businessSlug: businessDetails[businessId]?.slug || null, // Include both formats for compatibility
            status: 'pending',
            subtotal: subtotal,
            delivery_fee: deliveryFee,
            deliveryFee: deliveryFee, // Include both formats for compatibility
            service_fee: serviceFeeShare,
            serviceFee: serviceFeeShare, // Include both formats for compatibility
            total: total,
            preparation_time: preparationTime,
            preparationTime: preparationTime, // Include both formats for compatibility
            estimated_delivery_time: deliveryTime,
            estimatedDeliveryTime: deliveryTime, // Include both formats for compatibility
            delivery_distance_km: calculatedDistance, // Add the calculated distance
            deliveryDistanceKm: calculatedDistance, // Include both formats for compatibility
            delivery_method: deliveryMethod as 'delivery' | 'pickup',
            deliveryMethod: deliveryMethod as 'delivery' | 'pickup', // Include both formats for compatibility
            delivery_type: businessDeliveryType as 'asap' | 'scheduled',
            deliveryType: businessDeliveryType as 'asap' | 'scheduled', // Include both formats for compatibility
            scheduled_time: businessScheduledTime ? businessScheduledTime.toISOString() : null,
            scheduledTime: businessScheduledTime ? businessScheduledTime.toISOString() : null, // Include both formats for compatibility
            items: items.map(item => ({
              ...item,
              business_id: businessNumericId // Make sure each item has the business_id
            }))
          };
        })
      };

      // Log the order details before creating the order
      console.log('📦 CHECKOUT: Order details prepared:', {
        customerName: orderDetails.customerName,
        customerPhone: orderDetails.customerPhone,
        itemCount: orderDetails.items.length,
        businessCount: orderDetails.businesses.length,
        total: orderDetails.total
      });

      // Log business details
      console.log('🏪 CHECKOUT: Businesses in order:');
      orderDetails.businesses.forEach((business, index) => {
        console.log(`  Business ${index + 1}: ID=${business.business_id}, Name=${business.business_name}, Type=${business.business_type}`);
        console.log(`  Items: ${business.items?.length || 0}, Subtotal: ${business.subtotal}`);
      });

      // Get cart IDs for all businesses to link cart items to orders
      console.log('🆔 CHECKOUT: Getting cart IDs for businesses...');
      console.log('🆔 CHECKOUT: getBusinessCartIds function available:', typeof getBusinessCartIds);

      let businessCartIds = {};
      try {
        businessCartIds = await getBusinessCartIds();
        console.log('🆔 CHECKOUT: Retrieved cart IDs:', businessCartIds);
      } catch (error) {
        console.error('🆔 CHECKOUT: Error getting cart IDs:', error);
        businessCartIds = {};
      }

      console.log('🆔 CHECKOUT: Business IDs in order:', orderDetails.businesses.map(b => b.business_id));

      // Add cart IDs to the order details
      orderDetails.businesses = orderDetails.businesses.map(business => {
        const businessIdStr = business.business_id.toString();
        const cartId = businessCartIds[businessIdStr] || null;
        console.log(`🆔 CHECKOUT: Mapping business ${businessIdStr} to cart_id ${cartId}`);
        return {
          ...business,
          cart_id: cartId
        };
      });

      // Also add the primary cart ID to the main order details if available
      const primaryBusinessId = orderDetails.businesses[0]?.business_id?.toString();
      if (primaryBusinessId && businessCartIds[primaryBusinessId]) {
        orderDetails.cartId = businessCartIds[primaryBusinessId];
        console.log(`🆔 CHECKOUT: Set primary cart ID: ${orderDetails.cartId}`);
      }

      // Log the final order details with cart IDs
      console.log('🆔 CHECKOUT: Final order details with cart IDs:', JSON.stringify(orderDetails.businesses.map(b => ({
        business_id: b.business_id,
        business_name: b.business_name,
        cart_id: b.cart_id
      })), null, 2));

      // Create the order
      console.log('🔄 CHECKOUT: Calling orderService.createOrder()');
      const savedOrder = await orderService.createOrder(orderDetails);

      if (!savedOrder) {
        console.error('❌ CHECKOUT: Order creation failed - no order returned');
        throw new Error("Order creation failed - no order returned from API");
      }

      // Handle both single and multi-business orders
      if (!savedOrder.success) {
        console.error('❌ CHECKOUT: Order creation failed - API returned error:', savedOrder);
        throw new Error(savedOrder.error || savedOrder.message || "Order creation failed - API returned error");
      }

      // Determine the primary order number for the confirmation page
      let primaryOrderNumber: string;
      if (savedOrder.isMultiBusiness && savedOrder.orders && savedOrder.orders.length > 0) {
        // For multi-business orders, use the first order number (or ID if no order number)
        primaryOrderNumber = savedOrder.orders[0].orderNumber || savedOrder.orders[0].orderId.toString();
        console.log(`✅ CHECKOUT: Multi-business order created successfully - ${savedOrder.orderCount} orders created`);
        console.log('Order IDs:', savedOrder.orders.map((o: any) => `${o.businessName}: ${o.orderId} (${o.orderNumber || 'no order number'})`));
      } else if (savedOrder.orderNumber || savedOrder.orderId) {
        // For single business orders, prefer order number over order ID
        primaryOrderNumber = savedOrder.orderNumber || savedOrder.orderId.toString();
        console.log('✅ CHECKOUT: Single business order created successfully:', savedOrder.orderId, 'order number:', savedOrder.orderNumber);
      } else {
        console.error('❌ CHECKOUT: No order ID or order number returned from API:', savedOrder);
        throw new Error("Order creation failed - no order ID returned");
      }

      // Save order details to session storage
      if (typeof window !== 'undefined') {
        const orderDetailsToSave = {
          orderId: primaryOrderNumber,
          isMultiBusiness: savedOrder.isMultiBusiness || false,
          orderCount: savedOrder.orderCount || 1,
          orders: savedOrder.orders || [],
          businesses: orderDetails.businesses,
          items: cart,
          subtotal: orderDetails.subtotal,
          deliveryFee: orderDetails.deliveryFee,
          serviceFee: orderDetails.serviceFee,
          total: orderDetails.total,
          customerName: orderDetails.customerName,
          customerPhone: orderDetails.customerPhone,
          customerAddress: orderDetails.customerAddress,
          paymentMethod: orderDetails.paymentMethod,
          deliveryType: orderDetails.deliveryType,
          isGuestCheckout: orderDetails.isGuestCheckout
        };

        // Save order details to session storage
        sessionStorage.setItem('orderDetails', JSON.stringify(orderDetailsToSave));
        sessionStorage.removeItem('loopJerseyCheckoutFormState');

        // Save a copy of the cart before clearing it
        localStorage.setItem('loopJerseyLastCart', JSON.stringify(cart));

        // Remove the backup cart since we don't need it anymore
        localStorage.removeItem("loopJerseyCartBackup");

        // Set order processing state to prevent empty cart screen from showing
        setIsProcessingOrder(true);

        // Only clear the cart AFTER we've confirmed the order was created successfully
        clearCart();

        // Clear the session ID so the next order gets a fresh session ID
        if (clearSessionId) {
          clearSessionId();
        }

        // PHASE 2 STEP 4: Redirect based on order type (session ID for multi-business, order number for single)
        if (savedOrder.isMultiBusiness && sessionId) {
          console.log('🔄 CHECKOUT: Redirecting to multi-business order confirmation with session ID:', sessionId);
          window.location.href = `/order-confirmation?sessionId=${sessionId}`;
        } else {
          console.log('🔄 CHECKOUT: Redirecting to single business order confirmation with order number:', primaryOrderNumber);
          window.location.href = `/order-confirmation?orderNumber=${primaryOrderNumber}`;
        }
      }
    } catch (error: any) {
      console.error("❌ CHECKOUT: Error creating order:", error);

      // Restore cart from backup if available
      try {
        const backupCart = localStorage.getItem("loopJerseyCartBackup");
        if (backupCart) {
          console.log("🔄 CHECKOUT: Restoring cart from backup");
          // Don't actually restore it to the state - just make sure it's in localStorage
          // so it will be loaded on the next page
          localStorage.setItem("loopJerseyCart", backupCart);
        }
      } catch (backupError) {
        console.error("Error restoring cart from backup:", backupError);
      }

      // Show error toast
      toast({
        title: "Order Error",
        description: "There was a problem processing your order. Please try again.",
        variant: "destructive"
      });

      // Reset order processing state on error
      setIsProcessingOrder(false);
    } finally {
      setIsSubmitting(false);
    }
  }, [
    cart,
    validateAllSteps,
    checkBusinessClosure, // PHASE 4 STEP 9: Add business closure check dependency
    setIsSubmitting,
    user,
    firstName,
    lastName,
    phone,
    address,
    customerCoords,
    instructions,
    paymentMethod,
    totalPrice,
    totalDeliveryFee,
    serviceFee,
    grandTotal,
    globalDeliveryType,
    scheduledDeliveryTime,
    itemsByBusiness,
    businessNamesFunc,
    getDeliveryFee,
    // REMOVED: getPreparationTime, getDeliveryTime, getDeliveryType, getScheduledTime
    getDeliveryMethod,
    businessDetails,
    businessDeliveryTimes, // Add this dependency so delivery times are used in order creation
    businessDeliveryDistances, // Add this dependency so delivery distances are used in order creation
    clearCart,
    setIsProcessingOrder,
    getBusinessCartIds
  ]);

  // Update step completion state when validation results change
  useEffect(() => {
    console.log('🔄 STEP COMPLETION UPDATE:', {
      customerInfoValid,
      deliveryAddressValid,
      deliveryTimeValid,
      paymentMethodValid,
      isInitialized,
      isConnected,
      cartLength: cart.length,
      formValues: {
        firstName: firstName?.trim(),
        lastName: lastName?.trim(),
        phone: phone?.trim()
      }
    });

    setStepsCompleted(prev => ({
      ...prev,
      customerInfo: customerInfoValid,
      deliveryAddress: deliveryAddressValid,
      deliveryTime: deliveryTimeValid,
      paymentMethod: paymentMethodValid
    }));
  }, [customerInfoValid, deliveryAddressValid, deliveryTimeValid, paymentMethodValid, firstName, lastName, phone, isInitialized, isConnected, cart.length]);

  // Recalculate delivery fees when customer address changes
  useEffect(() => {
    // Only recalculate if we have a complete address and coordinates
    if (!customerCoords || !address || !parish || !postcode) {
      return;
    }

    // Only recalculate if we have businesses in the cart
    const businessIds = Object.keys(itemsByBusiness);
    if (businessIds.length === 0) {
      return;
    }

    // Prevent infinite loops by checking if we're already calculating
    if (isSubmitting) {
      return;
    }

    // Temporarily disable this logging to prevent infinite loops
    // console.log('🔄 CHECKOUT: Address changed, recalculating delivery fees...');

    // Recalculate delivery fees for all businesses with delivery method set to 'delivery'
    const recalculateDeliveryFees = async () => {
      for (const businessId of businessIds) {
        const deliveryMethod = getDeliveryMethod(parseInt(businessId, 10));

        // Only recalculate for businesses with delivery method set to 'delivery'
        if (deliveryMethod === 'delivery') {
          try {
            // Temporarily disable excessive logging
            // console.log(`💰 CHECKOUT: Recalculating delivery fee for business ${businessId}...`);

            // Get business details for this business
            const business = businessDetails[businessId];
            if (!business || !business.coordinates) {
              // console.warn(`CHECKOUT: No business details found for ${businessId}, skipping fee recalculation`);
              continue;
            }

            // Check if business offers delivery
            if (business.delivery_available === false) {
              // console.log(`🚫 CHECKOUT: Business ${businessId} is pickup-only, skipping delivery fee recalculation`);
              continue;
            }

            // Log the parameters being sent for debugging
            const apiParams = {
              businessId: parseInt(businessId, 10),
              businessCoordinates: business.coordinates,
              customerCoordinates: customerCoords,
              postcode: postcode,
              deliveryFeeModel: business.delivery_fee_model || 'mixed',
              deliveryFee: business.delivery_fee || 2.50,
              deliveryFeePerKm: business.delivery_fee_per_km || 0.50,
              preparationTimeMinutes: business.preparation_time_minutes || businessPreparationTimes[businessId] || 15
            };

            // Temporarily disable excessive logging
            // console.log(`💰 CHECKOUT: API parameters for business ${businessId}:`, apiParams);

            // Calculate new delivery fee using the API with timeout and better error handling
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

            const response = await fetch('/api/delivery/calculate-fee', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify(apiParams),
              signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (response.ok) {
              const feeData = await response.json();
              const newFee = feeData.fee;
              const deliveryTime = feeData.totalTimeMinutes;
              const distance = feeData.distance; // Capture the distance from API

              // Temporarily disable excessive logging
              // console.log(`💰 CHECKOUT: New delivery fee for business ${businessId}: £${newFee.toFixed(2)} (was £${getDeliveryFee(parseInt(businessId, 10)).toFixed(2)})`);
              // console.log(`🚚 CHECKOUT: New delivery time for business ${businessId}: ${deliveryTime} minutes`);
              // console.log(`📏 CHECKOUT: Distance for business ${businessId}: ${distance ? distance.toFixed(2) + ' km' : 'not available'}`);
              // console.log(`💰 CHECKOUT: Full API response for business ${businessId}:`, feeData);

              // Update the delivery fee in the cart context
              await setDeliveryFee(parseInt(businessId, 10), newFee);

              // Update the delivery time from the API calculation
              if (deliveryTime && !isNaN(deliveryTime)) {
                setBusinessDeliveryTimes(prev => ({
                  ...prev,
                  [businessId]: deliveryTime
                }));
              }

              // Store the distance for use in order creation
              if (distance && !isNaN(distance)) {
                setBusinessDeliveryDistances(prev => ({
                  ...prev,
                  [businessId]: distance
                }));
              }
            } else {
              const errorText = await response.text();
              console.error(`CHECKOUT: Failed to calculate delivery fee for business ${businessId}:`, response.status, errorText);

              // Keep the existing fee if API fails
              const currentFee = getDeliveryFee(parseInt(businessId, 10));
              console.log(`CHECKOUT: Keeping existing fee £${currentFee.toFixed(2)} for business ${businessId} due to API error`);
            }
          } catch (error) {
            console.error(`CHECKOUT: Error recalculating delivery fee for business ${businessId}:`, error);

            // Check if it's a network error
            if (error.name === 'AbortError') {
              console.error(`CHECKOUT: Request timeout for business ${businessId}`);
            } else if (error.message.includes('Failed to fetch')) {
              console.error(`CHECKOUT: Network error for business ${businessId} - check if server is running`);
            }

            // Keep the existing fee if calculation fails
            const currentFee = getDeliveryFee(parseInt(businessId, 10));
            console.log(`CHECKOUT: Keeping existing fee £${currentFee.toFixed(2)} for business ${businessId} due to error`);
          }
        }
      }
    };

    // Debounce the recalculation to avoid too many API calls
    const timeoutId = setTimeout(recalculateDeliveryFees, 1000);

    return () => clearTimeout(timeoutId);
  }, [customerCoords, address, parish, postcode, itemsByBusiness, businessDetails, businessPreparationTimes, getDeliveryMethod, getDeliveryFee, setDeliveryFee]);



  // Load form state from sessionStorage on initial render
  useEffect(() => {
    if (!isFormStateRestored) {
      const savedState = loadFormState();
      if (savedState) {
        setFirstName(savedState.firstName || '');
        setLastName(savedState.lastName || '');
        setPhone(savedState.phone || '');
        setAddress(savedState.address || '');
        setParish(savedState.parish || '');
        setPostcode(savedState.postcode || '');
        setInstructions(savedState.instructions || '');
        setPaymentMethod(savedState.paymentMethod || 'card');

        setGlobalDeliveryType(savedState.deliveryType || 'asap');
        setCustomerCoords(savedState.customerCoords);

        if (savedState.scheduledDeliveryTime) {
          setScheduledDeliveryTime(new Date(savedState.scheduledDeliveryTime));
        }

        console.log('Checkout form state restored from sessionStorage');
      }

      setIsFormStateRestored(true);
    }
  }, [isFormStateRestored]);

  // Separate effect for pre-filling user data when profile loads
  useEffect(() => {
    if (!userProfile || !isFormStateRestored) return;

    console.log('🏠 CHECKOUT: User profile loaded, checking pre-fill conditions:', {
      hasUserProfile: !!userProfile,
      userEmail: userProfile?.email,
      currentFirstName: firstName,
      currentAddress: address
    });

    // Pre-fill name fields if they're empty
    if (!firstName && userProfile.first_name) {
      console.log('🏠 CHECKOUT: Pre-filling first name:', userProfile.first_name);
      setFirstName(userProfile.first_name);
    } else if (!firstName && userProfile.name) {
      // If no first_name but has name, try to split it
      const nameParts = userProfile.name.split(' ');
      console.log('🏠 CHECKOUT: Pre-filling name from full name:', nameParts);
      setFirstName(nameParts[0] || '');
      if (nameParts.length > 1) {
        setLastName(nameParts.slice(1).join(' '));
      }
    }

    if (!lastName && userProfile.last_name) {
      console.log('🏠 CHECKOUT: Pre-filling last name:', userProfile.last_name);
      setLastName(userProfile.last_name);
    }

    // Pre-fill phone if available (but it's now optional)
    if (!phone && userProfile.phone) {
      console.log('🏠 CHECKOUT: Pre-filling phone:', userProfile.phone);
      setPhone(userProfile.phone);
    }

    // Note: Default address pre-filling has been removed
  }, [userProfile, isFormStateRestored, firstName, lastName, phone, address]);

  // Save form state to sessionStorage when it changes
  useEffect(() => {
    if (isFormStateRestored && firstName && lastName && phone && address) {
      const formState: CheckoutFormState = {
        paymentMethod,
        address,
        parish,
        customerCoords,
        firstName,
        lastName,
        phone,
        instructions,

        postcode,
        deliveryType: globalDeliveryType,
        scheduledDeliveryTime: scheduledDeliveryTime ? scheduledDeliveryTime.toISOString() : null
      };

      saveFormState(formState);
    }
  }, [
    isFormStateRestored,
    firstName,
    lastName,
    phone,
    address,
    parish,
    postcode,
    instructions,
    paymentMethod,

    globalDeliveryType,
    scheduledDeliveryTime,
    customerCoords
  ]);

  // Context value - memoized to prevent unnecessary re-renders
  const contextValue = useMemo(() => ({
    // Form state
    firstName,
    setFirstName,
    lastName,
    setLastName,
    phone,
    setPhone,
    address,
    setAddress,
    parish,
    setParish,
    postcode,
    setPostcode,
    instructions,
    setInstructions,
    paymentMethod,
    setPaymentMethod,

    globalDeliveryType,
    setGlobalDeliveryType,
    scheduledDeliveryTime,
    setScheduledDeliveryTime,
    customerCoords,
    setCustomerCoords,

    // UI state
    isSubmitting,
    setIsSubmitting,
    currentStep,
    setCurrentStep,

    showWelcomeDialog,
    setShowWelcomeDialog,

    // Step completion
    stepsCompleted,

    // Validation functions
    checkCustomerInfo,
    checkDeliveryAddress,
    checkDeliveryTime,
    checkPaymentMethod,
    validateAllSteps,

    // Navigation functions
    goToStep,
    goToNextStep,
    goToPrevStep,

    // Form submission
    handleSubmit,

    // Derived data
    isFormComplete,
    totalDeliveryFee,
    serviceFee,
    grandTotal,
    businessDeliveryTimes,
    itemsByBusiness,
    businessNames: businessNamesFunc,

    // Business data
    businessDetails,
    businessDeliveryFees,
    businessPreparationTimes,
    isLoadingBusinessDetails
  }), [
    // Form state
    firstName, lastName, phone, address, parish, postcode, instructions,
    paymentMethod, globalDeliveryType, scheduledDeliveryTime, customerCoords,

    // UI state
    isSubmitting, currentStep, showWelcomeDialog,

    // Step completion
    stepsCompleted,

    // Validation functions
    checkCustomerInfo, checkDeliveryAddress, checkDeliveryTime, checkPaymentMethod, validateAllSteps,

    // Navigation functions
    goToStep, goToNextStep, goToPrevStep,

    // Form submission
    handleSubmit,

    // Derived data
    isFormComplete, totalDeliveryFee, serviceFee, grandTotal, businessDeliveryTimes, businessDeliveryDistances,
    itemsByBusiness, businessNamesFunc,

    // Business data
    businessDetails, businessDeliveryFees, businessPreparationTimes, isLoadingBusinessDetails
  ]);

  return (
    <CheckoutContext.Provider value={contextValue}>
      {children}
    </CheckoutContext.Provider>
  );
};

// Custom hook to use the checkout context
export const useCheckout = () => {
  const context = useContext(CheckoutContext);
  if (context === undefined) {
    throw new Error("useCheckout must be used within a CheckoutProvider");
  }
  return context;
};
