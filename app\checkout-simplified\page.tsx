"use client"

import React, { useState, useEffect, useMemo } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { SimpleCheckbox } from "@/components/ui/simple-checkbox"
import { SimpleRadioGroup, SimpleRadioGroupItem } from "@/components/ui/simple-radio-group"
import { useRealtimeCart } from "@/context/realtime-cart-context"
import { useAuth } from "@/context/unified-auth-context"
import { Loader2 } from "lucide-react"
import Link from "next/link"

export default function CheckoutSimplifiedPage() {
  const { cart, totalPrice, getItemsByBusiness, getBusinessNames, isProcessingOrder } = useRealtimeCart()
  const { user } = useAuth()

  // Basic form state
  const [firstName, setFirstName] = useState("")
  const [lastName, setLastName] = useState("")
  const [email, setEmail] = useState("")
  const [phone, setPhone] = useState("")
  const [address, setAddress] = useState("")
  const [postcode, setPostcode] = useState("")
  const [saveAddress, setSaveAddress] = useState(false)
  const [paymentMethod, setPaymentMethod] = useState("card")
  const [deliveryType, setDeliveryType] = useState("asap")

  // Memoized values
  const itemsByBusiness = useMemo(() => getItemsByBusiness(), [getItemsByBusiness])
  const businessNames = useMemo(() => getBusinessNames(), [getBusinessNames])

  // Form validation - phone is now optional
  const isFormValid = useMemo(() => {
    return firstName && lastName && address && postcode
  }, [firstName, lastName, address, postcode])

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!isFormValid) {
      alert("Please fill in all required fields")
      return
    }

    alert("Order submitted successfully! This is a simplified demo.")
  }

  // Return early if cart is empty
  if (cart.length === 0) {
    // Show loading screen if order is being processed
    if (isProcessingOrder) {
      return (
        <div className="container mx-auto px-4 py-8">
          <h1 className="text-2xl font-bold mb-6">Checkout (Simplified)</h1>
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <div className="text-center">
              <Loader2 className="h-12 w-12 mx-auto text-emerald-600 animate-spin mb-4" />
              <h2 className="text-xl font-semibold mb-2">Processing your order...</h2>
              <p className="text-gray-600">Please wait while we confirm your order</p>
            </div>
          </div>
        </div>
      );
    }

    // Show empty cart screen
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-6">Checkout (Simplified)</h1>
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <p className="text-center text-gray-600 mb-4">Your cart is empty</p>
          <div className="flex flex-col items-center gap-4">
            <Button asChild>
              <Link href="/businesses">Browse Businesses</Link>
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Checkout (Simplified)</h1>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Customer Information */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4">Customer Information</h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <Label htmlFor="firstName">First Name</Label>
                  <Input
                    id="firstName"
                    value={firstName}
                    onChange={(e) => setFirstName(e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="lastName">Last Name</Label>
                  <Input
                    id="lastName"
                    value={lastName}
                    onChange={(e) => setLastName(e.target.value)}
                    required
                  />
                </div>
              </div>

              <div className="mb-4">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>

              <div className="mb-4">
                <Label htmlFor="phone">Phone <span className="text-gray-500 text-sm">(optional)</span></Label>
                <Input
                  id="phone"
                  value={phone}
                  onChange={(e) => setPhone(e.target.value)}
                  placeholder="07797123456"
                />
              </div>
            </div>

            {/* Delivery Address */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4">Delivery Address</h2>

              <div className="mb-4">
                <Label htmlFor="address">Address</Label>
                <Input
                  id="address"
                  value={address}
                  onChange={(e) => setAddress(e.target.value)}
                  required
                />
              </div>

              <div className="mb-4">
                <Label htmlFor="postcode">Postcode</Label>
                <Input
                  id="postcode"
                  value={postcode}
                  onChange={(e) => setPostcode(e.target.value)}
                  required
                />
              </div>

              {user && (
                <div className="flex items-center space-x-2 p-3 bg-emerald-50 rounded-lg">
                  <SimpleCheckbox
                    id="saveAddress"
                    checked={saveAddress}
                    onCheckedChange={(checked) => setSaveAddress(checked)}
                  />
                  <Label htmlFor="saveAddress">Save this address for future orders</Label>
                </div>
              )}
            </div>

            {/* Delivery Time */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4">Delivery Time</h2>

              <SimpleRadioGroup
                value={deliveryType}
                onValueChange={setDeliveryType}
                className="space-y-3"
              >
                <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
                  <SimpleRadioGroupItem value="asap" id="asap" />
                  <Label htmlFor="asap">As soon as possible</Label>
                </div>
                <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
                  <SimpleRadioGroupItem value="scheduled" id="scheduled" />
                  <Label htmlFor="scheduled">Schedule for later</Label>
                </div>
              </SimpleRadioGroup>
            </div>

            {/* Payment Method */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4">Payment Method</h2>

              <SimpleRadioGroup
                value={paymentMethod}
                onValueChange={setPaymentMethod}
                className="space-y-3"
              >
                <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
                  <SimpleRadioGroupItem value="card" id="card" />
                  <Label htmlFor="card">Credit/Debit Card</Label>
                </div>
                <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
                  <SimpleRadioGroupItem value="cash" id="cash" />
                  <Label htmlFor="cash">Cash on Delivery</Label>
                </div>
              </SimpleRadioGroup>
            </div>

            <Button
              type="submit"
              className="w-full bg-emerald-600 hover:bg-emerald-700"
              disabled={!isFormValid}
            >
              Place Order
            </Button>
          </form>
        </div>

        {/* Order Summary */}
        <div>
          <div className="bg-white rounded-lg shadow-md p-6 sticky top-4">
            <h2 className="text-xl font-semibold mb-4">Order Summary</h2>

            <div className="space-y-4 mb-4">
              {Object.keys(itemsByBusiness).map((businessId) => (
                <div key={businessId} className="border-b pb-3">
                  <h3 className="font-medium">{businessNames[businessId] || `Business ${businessId}`}</h3>
                  <div className="space-y-2 mt-2">
                    {itemsByBusiness[businessId].map((item) => (
                      <div key={item.id} className="flex justify-between text-sm">
                        <span>{item.quantity}x {item.name}</span>
                        <span>£{(item.price * item.quantity).toFixed(2)}</span>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>

            <div className="border-t pt-3 space-y-2">
              <div className="flex justify-between">
                <span>Subtotal:</span>
                <span>£{totalPrice.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span>Delivery Fee:</span>
                <span>£2.50</span>
              </div>
              <div className="flex justify-between">
                <span>Service Fee:</span>
                <span>£0.50</span>
              </div>
              <div className="flex justify-between font-bold text-lg pt-2">
                <span>Total:</span>
                <span>£{(totalPrice + 2.50 + 0.50).toFixed(2)}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
