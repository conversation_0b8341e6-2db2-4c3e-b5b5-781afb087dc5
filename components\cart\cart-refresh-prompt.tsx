'use client'

import { useState } from 'react'
import { useCart } from '@/context/realtime-cart-context'

interface CartRefreshPromptProps {
  error: string
  details?: {
    unavailableItems?: string[]
    priceChangedItems?: Array<{
      name: string
      oldPrice: number
      newPrice: number
    }>
  }
  onClose?: () => void
  onRefreshComplete?: (success: boolean, message: string) => void
}

export default function CartRefreshPrompt({ 
  error, 
  details, 
  onClose, 
  onRefreshComplete 
}: CartRefreshPromptProps) {
  const { refreshCart } = useCart()
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [refreshResult, setRefreshResult] = useState<{
    success: boolean
    message: string
  } | null>(null)

  const handleRefresh = async () => {
    setIsRefreshing(true)
    try {
      const result = await refreshCart()
      setRefreshResult(result)
      if (onRefreshComplete) {
        onRefreshComplete(result.success, result.message)
      }
    } catch (error) {
      const errorMessage = 'Failed to refresh cart'
      setRefreshResult({
        success: false,
        message: errorMessage
      })
      if (onRefreshComplete) {
        onRefreshComplete(false, errorMessage)
      }
    } finally {
      setIsRefreshing(false)
    }
  }

  return (
    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        </div>
        <div className="ml-3 flex-1">
          <h3 className="text-sm font-medium text-yellow-800">
            Cart Update Required
          </h3>
          <div className="mt-2 text-sm text-yellow-700">
            <p>{error}</p>
            
            {details && (
              <div className="mt-3 space-y-2">
                {details.unavailableItems && details.unavailableItems.length > 0 && (
                  <div>
                    <p className="font-medium">Items no longer available:</p>
                    <ul className="list-disc list-inside ml-2">
                      {details.unavailableItems.map((item, index) => (
                        <li key={index}>{item}</li>
                      ))}
                    </ul>
                  </div>
                )}
                
                {details.priceChangedItems && details.priceChangedItems.length > 0 && (
                  <div>
                    <p className="font-medium">Items with price changes:</p>
                    <ul className="list-disc list-inside ml-2">
                      {details.priceChangedItems.map((item, index) => (
                        <li key={index}>
                          {item.name}: £{item.oldPrice.toFixed(2)} → £{item.newPrice.toFixed(2)}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}
          </div>
          
          {refreshResult && (
            <div className={`mt-3 p-2 rounded ${
              refreshResult.success 
                ? 'bg-green-100 text-green-800' 
                : 'bg-red-100 text-red-800'
            }`}>
              <p className="text-sm">{refreshResult.message}</p>
            </div>
          )}
          
          <div className="mt-4 flex space-x-3">
            <button
              onClick={handleRefresh}
              disabled={isRefreshing}
              className="bg-yellow-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-yellow-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isRefreshing ? 'Refreshing...' : 'Refresh Cart'}
            </button>
            
            {onClose && (
              <button
                onClick={onClose}
                className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-400"
              >
                Close
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
