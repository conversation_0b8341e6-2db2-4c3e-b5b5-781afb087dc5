import { NextResponse } from 'next/server';
import {
  getUserLocation,
  updateUserLocation,
  UserLocationData,
  clearUserLocation
} from '@/lib/session-utils';
import {
  standardizeJerseyPostcode,
  getCoordinatesFromPostcode,
  getParishFromPostcode
} from '@/lib/jersey-postcodes';

// Set cache control headers
const headers = {
  'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
  'Pragma': 'no-cache',
  'Expires': '0'
};

/**
 * GET handler for user location API
 * Returns the user's saved location from the session
 */
export async function GET() {
  try {
    // Get user location from session
    const location = await getUserLocation();

    return NextResponse.json({ location }, { headers });
  } catch (error) {
    console.error('Error in GET /api/user/location:', error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500, headers }
    );
  }
}

/**
 * POST handler for user location API
 * Updates the user's location in the session
 */
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { postcode } = body;

    if (!postcode) {
      return NextResponse.json(
        { error: "Postcode is required" },
        { status: 400, headers }
      );
    }

    // Standardize the postcode
    const standardized = await standardizeJerseyPostcode(postcode);
    if (!standardized) {
      return NextResponse.json(
        { error: "Invalid postcode format" },
        { status: 400, headers }
      );
    }

    // Get coordinates and parish
    const coordinates = await getCoordinatesFromPostcode(standardized);
    const parish = await getParishFromPostcode(standardized);

    // Update user location
    const location = await updateUserLocation(standardized, coordinates, parish);

    return NextResponse.json({ location }, { headers });
  } catch (error) {
    console.error('Error in POST /api/user/location:', error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500, headers }
    );
  }
}

/**
 * DELETE handler for user location API
 * Clears the user's location from the session
 */
export async function DELETE() {
  try {
    // Get current location before clearing
    const location = await getUserLocation();

    // Clear the location cookie
    await clearUserLocation();

    if (location) {
      // Return a cleared location object for the client
      const clearedLocation: UserLocationData = {
        ...location,
        postcode: '',
        coordinates: null,
        parish: null,
        lastUpdated: Date.now()
      };

      return NextResponse.json({ location: clearedLocation }, { headers });
    }

    return NextResponse.json({ message: "No location to clear" }, { headers });
  } catch (error) {
    console.error('Error in DELETE /api/user/location:', error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500, headers }
    );
  }
}
