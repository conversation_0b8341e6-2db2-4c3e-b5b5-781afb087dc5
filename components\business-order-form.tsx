"use client"

import { useMemo } from "react"
import { ShoppingBag } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { useRealtimeCart } from "@/context/realtime-cart-context"
import CheckoutLink from "@/components/checkout-link"

interface BusinessOrderFormProps {
  businessId: number
  businessSlug: string
  businessName: string
  deliveryTime?: string
  deliveryFee?: string
  minimumOrder?: number
}

// Helper function to format item details
const formatItemDetails = (item: any) => {
  const details: string[] = [];

  // Add variant name if available
  if (item.variantName && item.variantName !== item.name) {
    details.push(item.variantName);
  }

  // Add customizations if available
  if (item.customizations && item.customizations.length > 0) {
    item.customizations.forEach((customization: any) => {
      if (customization.selectedOptions && customization.selectedOptions.length > 0) {
        customization.selectedOptions.forEach((option: any) => {
          details.push(option.name);
        });
      }
    });
  }

  // Fallback to legacy options if no variant/customization data
  if (details.length === 0 && item.options && item.options.length > 0) {
    details.push(...item.options);
  }

  return details;
};

export default function BusinessOrderForm({
  businessId,
  businessSlug,
  businessName,
  deliveryTime,
  deliveryFee,
  minimumOrder
}: BusinessOrderFormProps) {
  const { getItemsByBusiness, getDeliveryMethod } = useRealtimeCart()

  // Get items for this business
  const businessItems = useMemo(() => {
    const allItemsByBusiness = getItemsByBusiness()
    return allItemsByBusiness[businessId.toString()] || []
  }, [getItemsByBusiness, businessId])

  // Calculate subtotal
  const businessSubtotal = useMemo(() => {
    return businessItems.reduce((sum, item) => sum + (item.price * item.quantity), 0)
  }, [businessItems])

  // Get delivery method
  const deliveryMethod = getDeliveryMethod(businessId)

  // Parse delivery fee
  const deliveryFeeNumeric = useMemo(() => {
    if (!deliveryFee) return 0
    if (deliveryFee.toLowerCase().includes('free')) return 0
    const match = deliveryFee.match(/£?(\d+\.?\d*)/)
    return match ? parseFloat(match[1]) : 0
  }, [deliveryFee])

  // Calculate total
  const total = businessSubtotal + (businessItems.length > 0 ? deliveryFeeNumeric + 0.5 : 0)

  return (
    <div className="bg-white rounded-lg shadow-md p-6 border border-gray-100 sticky top-[120px] max-h-[calc(100vh-140px)] overflow-y-auto">
      <div className="flex items-center mb-4">
        <ShoppingBag className="mr-2 text-emerald-600" />
        <h3 className="text-lg font-semibold">Your Order with {businessName}</h3>
      </div>

      {businessItems.length === 0 ? (
        <div className="border-t border-b py-4 my-4">
          <p className="text-center text-gray-500">Add items from this business to your order</p>
          <p className="text-center text-sm text-emerald-600 mt-2">
            You can order from multiple businesses at once!
          </p>
        </div>
      ) : (
        <div className="border-t pt-4 my-4">
          <div className="max-h-60 overflow-y-auto mb-4">
            {businessItems.map((item) => (
              <div key={item.cartItemId || `${item.id}-${Math.random()}`} className="flex justify-between mb-3">
                <div>
                  <p className="font-medium">
                    {item.quantity}x {item.name}
                  </p>
                  {(() => {
                    const details = formatItemDetails(item);
                    return details.length > 0 && (
                      <p className="text-sm text-gray-500">{details.join(", ")}</p>
                    );
                  })()}
                </div>
                <p className="font-medium">£{(item.price * item.quantity).toFixed(2)}</p>
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="space-y-4">
        <div className="flex justify-between text-sm">
          <span>Subtotal</span>
          <span>£{businessSubtotal.toFixed(2)}</span>
        </div>
        {businessItems.length > 0 && (
          <>
            <div className="flex justify-between text-sm">
              <span>Delivery Fee</span>
              <span>{deliveryFee || 'Free'}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Service Fee</span>
              <span>£0.50</span>
            </div>
          </>
        )}
        <div className="flex justify-between font-semibold pt-4 border-t">
          <span>{businessName} Total</span>
          <span>£{total.toFixed(2)}</span>
        </div>
      </div>

      {businessItems.length > 0 ? (
        <CheckoutLink
          buttonClassName={`w-full mt-6 ${
            deliveryMethod === 'pickup'
              ? 'bg-orange-600 hover:bg-orange-700'
              : 'bg-blue-600 hover:bg-blue-700'
          }`}
        >
          Go to checkout
        </CheckoutLink>
      ) : (
        <Button
          className={`w-full mt-6 ${
            deliveryMethod === 'pickup'
              ? 'bg-orange-600 hover:bg-orange-700'
              : 'bg-blue-600 hover:bg-blue-700'
          }`}
          disabled
        >
          Go to checkout
        </Button>
      )}

      {/* Additional info */}
      {deliveryTime && (
        <div className="mt-4 text-sm text-gray-600 text-center">
          <p>Estimated delivery: {deliveryTime}</p>
        </div>
      )}

      {minimumOrder && businessSubtotal < minimumOrder && businessItems.length > 0 && (
        <div className="mt-2 text-sm text-orange-600 text-center">
          <p>Minimum order: £{minimumOrder.toFixed(2)}</p>
          <p>Add £{(minimumOrder - businessSubtotal).toFixed(2)} more</p>
        </div>
      )}
    </div>
  )
}
