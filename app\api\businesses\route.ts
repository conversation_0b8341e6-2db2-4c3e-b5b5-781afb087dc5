import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create a Supabase client with the service role key for admin access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const adminClient = createClient(supabaseUrl, supabaseServiceKey);

export async function GET(request: Request) {
  try {
    console.log('Businesses API called');

    // Parse query parameters
    const url = new URL(request.url);
    const type = url.searchParams.get('type');
    const search = url.searchParams.get('search');
    const parish = url.searchParams.get('parish');
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '20');

    // Build query - only show approved businesses in public search
    let query = adminClient
      .from('businesses')
      .select('*, business_types(id, name, slug)')
      .eq('is_approved', true)
      .order('name');

    // Apply filters
    if (type) {
      // First get the business type ID
      const { data: businessType, error: typeError } = await adminClient
        .from('business_types')
        .select('id')
        .eq('slug', type)
        .single();

      if (typeError) {
        console.error('Error fetching business type:', typeError);
      } else if (businessType) {
        query = query.eq('business_type_id', businessType.id);
      }
    }

    if (search) {
      query = query.ilike('name', `%${search}%`);
    }

    if (parish) {
      query = query.eq('parish', parish);
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    // Execute query
    const { data: businesses, error, count } = await query;

    if (error) {
      console.error('Error fetching businesses:', error);
      return NextResponse.json(
        { error: `Failed to fetch businesses: ${error.message}` },
        { status: 500 }
      );
    }

    // Transform businesses to include type slug
    const transformedBusinesses = businesses.map(business => {
      // Log the business delivery model for debugging
      console.log(`Business ${business.name} (${business.id}) delivery model: ${business.delivery_fee_model}, fee: ${business.delivery_fee}, fee per km: ${business.delivery_fee_per_km}`);

      // Check for potential issues with the delivery fee model
      if (business.delivery_fee_model === 'mixed' && (business.delivery_fee_per_km === undefined || business.delivery_fee_per_km === 0)) {
        console.warn(`WARNING: Business ${business.name} (${business.id}) has mixed model but fee_per_km is ${business.delivery_fee_per_km}`);
      }

      return {
        ...business,
        business_type_slug: business.business_types?.slug || 'unknown'
      };
    });

    return NextResponse.json({
      businesses: transformedBusinesses,
      pagination: {
        page,
        limit,
        total: count || 0
      }
    });
  } catch (error) {
    console.error('Unexpected error in businesses API:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: error.message },
      { status: 500 }
    );
  }
}
